"""Proper TTM Squeeze Scanner - Matches Think or Swim Logic

This scanner implements the standard TTM Squeeze detection that matches
what Think or Swim and other platforms use:

1. Squeeze Condition: Bollinger Bands inside Keltner Channels
2. Squeeze Release: Bollinger Bands break outside Keltner Channels
3. Momentum: Using proper momentum oscillator
4. Histogram: Momentum histogram analysis

This should find the same squeezes that Think or Swim finds.
"""
from __future__ import annotations

import asyncio
import aiohttp
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import requests

try:
    from config import get_api_key
except ImportError:
    def get_api_key(key_name):
        import os
        # Try to load from config.env file
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.env')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        if '=' in line:
                            env_key, env_value = line.strip().split('=', 1)
                            if env_key == key_name:
                                return env_value
        return os.getenv(key_name)

try:
    from logger_util import info, warning
except ImportError:
    def info(msg): print(f"INFO: {msg}")
    def warning(msg): print(f"WARNING: {msg}")

# Get API key - use environment variable or hardcoded key
import os
FMP_API_KEY = os.getenv('FMP_API_KEY') or 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
FMP_BASE_URL = 'https://financialmodelingprep.com/api/v3'

# Large cap stocks for scanning
LARGE_CAP_STOCKS = [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'TSLA', 'META', 'BRK.B', 'UNH', 'JNJ',
    'V', 'WMT', 'XOM', 'LLY', 'JPM', 'PG', 'MA', 'HD', 'CVX', 'ABBV',
    'BAC', 'ORCL', 'KO', 'AVGO', 'PEP', 'COST', 'TMO', 'MRK', 'ACN', 'CSCO',
    'ABT', 'DHR', 'VZ', 'ADBE', 'CRM', 'NKE', 'TXN', 'LIN', 'WFC', 'NEE',
    'RTX', 'PM', 'UPS', 'QCOM', 'SPGI', 'HON', 'T', 'INTU', 'COP', 'IBM'
]

class ProperTTMSqueezeScanner:
    """Proper TTM Squeeze scanner that matches Think or Swim logic."""
    
    def __init__(self):
        self.api_key = FMP_API_KEY
        if not self.api_key:
            raise ValueError("FMP_API_KEY not found! Please set your API key in config/config.env")
    
    def get_historical_data(self, symbol: str, timeframe: str = '5min', days: int = 30) -> Optional[pd.DataFrame]:
        """Get historical data for a symbol."""
        try:
            # Map timeframes
            timeframe_map = {
                '1min': '1min',
                '5min': '5min', 
                '15min': '15min',
                '30min': '30min',
                '1hour': '1hour',
                '1day': 'daily-price-full'
            }
            
            if timeframe == '1day':
                url = f"{FMP_BASE_URL}/historical-price-full/{symbol}?apikey={self.api_key}"
            else:
                tf = timeframe_map.get(timeframe, '5min')
                url = f"{FMP_BASE_URL}/historical-chart/{tf}/{symbol}?apikey={self.api_key}"
            
            response = requests.get(url, timeout=10)
            data = response.json()
            
            if timeframe == '1day' and 'historical' in data:
                df = pd.DataFrame(data['historical'])
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            else:
                return None
            
            if df.empty:
                return None
            
            # Ensure we have the required columns
            required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_cols):
                return None
            
            # Convert to proper types
            df['date'] = pd.to_datetime(df['date'])
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Sort by date (oldest first)
            df = df.sort_values('date').reset_index(drop=True)
            
            # Take last N days of data
            if len(df) > days * 100:  # Rough estimate for intraday data
                df = df.tail(days * 100)
            
            return df
            
        except Exception as e:
            warning(f"Failed to get data for {symbol}: {e}")
            return None
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20, std_dev: float = 2.0) -> pd.DataFrame:
        """Calculate Bollinger Bands."""
        df = df.copy()
        
        # Simple Moving Average
        df['bb_middle'] = df['close'].rolling(window=period).mean()
        
        # Standard deviation
        df['bb_std'] = df['close'].rolling(window=period).std()
        
        # Upper and lower bands
        df['bb_upper'] = df['bb_middle'] + (df['bb_std'] * std_dev)
        df['bb_lower'] = df['bb_middle'] - (df['bb_std'] * std_dev)
        
        return df
    
    def calculate_keltner_channels(self, df: pd.DataFrame, period: int = 20, multiplier: float = 1.5) -> pd.DataFrame:
        """Calculate Keltner Channels."""
        df = df.copy()
        
        # Exponential Moving Average of close
        df['kc_middle'] = df['close'].ewm(span=period).mean()
        
        # True Range
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['close'].shift(1))
        df['tr3'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
        
        # Average True Range
        df['atr'] = df['true_range'].ewm(span=period).mean()
        
        # Keltner Channels
        df['kc_upper'] = df['kc_middle'] + (df['atr'] * multiplier)
        df['kc_lower'] = df['kc_middle'] - (df['atr'] * multiplier)
        
        return df
    
    def calculate_momentum(self, df: pd.DataFrame, period: int = 12) -> pd.DataFrame:
        """Calculate momentum oscillator (like Think or Swim)."""
        df = df.copy()
        
        # Linear regression of close prices
        def linear_regression_slope(series):
            if len(series) < 2:
                return 0
            x = np.arange(len(series))
            y = series.values
            if np.isnan(y).any():
                return 0
            slope = np.polyfit(x, y, 1)[0]
            return slope
        
        # Calculate momentum as linear regression slope
        df['momentum'] = df['close'].rolling(window=period).apply(linear_regression_slope, raw=False)
        
        # Normalize momentum
        df['momentum'] = df['momentum'] * 100
        
        return df
    
    def detect_ttm_squeeze(self, df: pd.DataFrame) -> Dict:
        """Detect TTM Squeeze conditions using STRICT 5 criteria."""
        if len(df) < 50:
            return {'meets_criteria': False, 'reason': 'Insufficient data'}

        # Calculate ALL indicators first
        df = self.calculate_bollinger_bands(df)
        df = self.calculate_keltner_channels(df)
        df = self.calculate_momentum(df)

        # Calculate 8EMA
        df['ema8'] = df['close'].ewm(span=8).mean()

        # Calculate ATR for ATR flip detection
        df['high_low'] = df['high'] - df['low']
        df['high_close'] = abs(df['high'] - df['close'].shift(1))
        df['low_close'] = abs(df['low'] - df['close'].shift(1))
        df['true_range'] = df[['high_low', 'high_close', 'low_close']].max(axis=1)
        df['atr'] = df['true_range'].rolling(window=14).mean()

        # Get latest values
        latest = df.iloc[-1]

        # ENHANCED CRITERIA: CORE MOMENTUM + PROFITABILITY FILTERS
        core_criteria = {}
        profitability_filters = {}
        bonus_criteria = {}

        # PROFITABILITY FILTERS (Must pass these first!)

        # 1. PRICE RANGE FILTER (Reasonable range for trading)
        price_in_range = 15 <= latest['close'] <= 300  # More realistic range
        profitability_filters['price_in_range'] = price_in_range

        # 2. VOLUME FILTER (ENHANCED - Significantly higher volume requirements)
        if len(df) >= 20:
            avg_volume_20 = df['volume'].rolling(20).mean().iloc[-1]
            current_volume = latest['volume']
            volume_above_average = current_volume > (avg_volume_20 * 1.5)  # 150% of average (increased from 120%)

            # Check for volume trend (stricter requirement)
            recent_volumes = df['volume'].iloc[-5:].values
            volume_trending = recent_volumes[-1] > recent_volumes[-3] and recent_volumes[-2] > recent_volumes[-4]  # Sustained volume increase
        else:
            volume_above_average = False
            volume_trending = False
        profitability_filters['volume_above_average'] = volume_above_average
        profitability_filters['volume_trending'] = volume_trending

        # 3. MOMENTUM STRENGTH FILTER (ENHANCED - Much stronger momentum requirement)
        momentum_strength = abs(latest['momentum'])
        strong_momentum = momentum_strength > 1.5  # Increased from 0.5 for stronger signals
        profitability_filters['strong_momentum'] = strong_momentum

        # 4. VOLATILITY FILTER (Reasonable movement for profit)
        if len(df) >= 14:
            price_range_14 = (df['high'].rolling(14).max() - df['low'].rolling(14).min()).iloc[-1]
            current_price = latest['close']
            volatility_sufficient = (price_range_14 / current_price) > 0.03  # 3% range minimum (more realistic)
        else:
            volatility_sufficient = False
        profitability_filters['volatility_sufficient'] = volatility_sufficient

        # CORE CRITERIA (Tightened for quality)

        # 1. 8EMA RISING (ENHANCED - Sustained trend requirement)
        if len(df) >= 8:
            ema8_values = df['ema8'].iloc[-5:].values  # Last 5 values for stronger confirmation
            # Require 4 consecutive rising periods (not just 3)
            ema8_rising = all(ema8_values[i] < ema8_values[i+1] for i in range(len(ema8_values)-1))
            # Price should be above EMA8 for bullish bias AND showing momentum
            price_above_ema = latest['close'] > latest['ema8']
            # Additional requirement: EMA8 should be significantly above its value 5 periods ago
            ema8_momentum = (ema8_values[-1] - ema8_values[0]) / ema8_values[0] > 0.005  # 0.5% minimum rise
            ema8_rising = ema8_rising and price_above_ema and ema8_momentum
        else:
            ema8_rising = False
        core_criteria['ema8_rising'] = ema8_rising

        # 2. ATR FLIP (ENHANCED - Decisive volatility expansion)
        if len(df) >= 15:
            atr_values = df['atr'].iloc[-10:].values  # Last 10 ATR values for better analysis
            # Require significant ATR increase, not just marginal
            atr_recent_avg = np.mean(atr_values[-3:])
            atr_previous_avg = np.mean(atr_values[-8:-5])
            # ATR must increase by at least 15% to be considered decisive
            atr_increase_pct = (atr_recent_avg - atr_previous_avg) / atr_previous_avg
            atr_flip = atr_increase_pct > 0.15  # 15% minimum increase (much more decisive)
        else:
            atr_flip = False
        core_criteria['atr_flip'] = atr_flip

        # 3. HISTOGRAM PATTERN (ENHANCED - Momentum IMPROVEMENT, not positive requirement)
        if len(df) >= 8:
            momentum_values = df['momentum'].iloc[-6:].values  # Last 6 momentum values for pattern analysis

            # KEY CONCEPT: Look for "less negative" = improving momentum, not just positive values
            # This catches early-stage breakouts before they become obvious

            # Check for sustained momentum improvement (can be negative but improving)
            current_momentum = momentum_values[-1]
            prev_momentum = momentum_values[-2]
            prev2_momentum = momentum_values[-3]

            # Primary requirement: Current bar is "less negative" (closer to zero) than previous
            momentum_improving = current_momentum > prev_momentum

            # Secondary requirement: Show improvement trend over multiple bars
            two_bar_improvement = current_momentum > prev2_momentum

            # Tertiary requirement: Significant improvement over 5-period lookback
            five_period_improvement = current_momentum > momentum_values[-5] + 0.2  # Meaningful improvement

            # Pattern is valid if momentum is improving, even if still negative
            # Examples: -2.5 → -1.8 (valid), -1.0 → -0.3 (valid), -0.5 → +0.2 (valid)
            histogram_pattern = momentum_improving and two_bar_improvement and five_period_improvement

        else:
            histogram_pattern = False
        core_criteria['histogram_pattern'] = histogram_pattern

        # 4. MOMENTUM RISING (ENHANCED - Sustained upward momentum trend)
        if len(df) >= 8:
            momentum_values = df['momentum'].iloc[-6:].values  # Last 6 momentum values for better trend analysis
            # Require sustained upward trend over multiple periods
            momentum_rising = (momentum_values[-1] > momentum_values[-2] and
                             momentum_values[-2] > momentum_values[-4] and
                             momentum_values[-1] > momentum_values[-5])  # Multiple confirmations
            # Additional requirement: momentum should be accelerating
            recent_slope = momentum_values[-1] - momentum_values[-3]
            earlier_slope = momentum_values[-3] - momentum_values[-5]
            momentum_accelerating = recent_slope > earlier_slope  # Momentum is accelerating
            momentum_rising = momentum_rising and momentum_accelerating
        else:
            momentum_rising = False
        core_criteria['momentum_rising'] = momentum_rising

        # BONUS CRITERIA (Nice to have, not required!)

        # 5. BB INSIDE KC (ENHANCED - Decisive squeeze condition, not marginal)
        bb_inside_kc = (latest['bb_upper'] <= latest['kc_upper'] and
                       latest['bb_lower'] >= latest['kc_lower'])
        if bb_inside_kc:
            # Check how decisively BB is inside KC (not just barely)
            upper_margin = (latest['kc_upper'] - latest['bb_upper']) / latest['close']
            lower_margin = (latest['bb_lower'] - latest['kc_lower']) / latest['close']
            # Require meaningful margin, not just barely inside
            decisive_squeeze = upper_margin > 0.005 and lower_margin > 0.005  # 0.5% minimum margin
            bb_inside_kc = decisive_squeeze
        bonus_criteria['bb_inside_kc'] = bb_inside_kc

        # PROFITABILITY-FOCUSED SCORING SYSTEM
        core_count = sum(core_criteria.values())  # Out of 4
        profitability_count = sum(profitability_filters.values())  # Out of 4
        has_squeeze_bonus = bb_inside_kc

        # ENHANCED PROFITABILITY REQUIREMENTS (Stricter for high-quality setups)
        passes_profitability = profitability_count >= 3  # Need 3 out of 4 profitability filters

        # Quality levels based on 4 CORE criteria + PROFITABILITY (Squeeze is BONUS only)
        # Core criteria: 8EMA Rising, ATR Flip, Histogram Pattern, Momentum Rising (4 total)
        meets_all_core = core_count == 4 and passes_profitability  # All 4 core + 3+ profitable
        meets_most_core = core_count >= 3 and profitability_count >= 3  # 3+ core + 3+ profitable
        meets_some_core = core_count >= 2 and profitability_count >= 3  # 2+ core + 3+ profitable

        # Combine all criteria for compatibility
        all_criteria = {**core_criteria, **profitability_filters, **bonus_criteria}
        total_criteria_count = core_count + profitability_count + (1 if has_squeeze_bonus else 0)

        # Define quality levels - SQUEEZE IS BONUS ONLY, NOT REQUIRED
        meets_all_criteria = meets_all_core  # Perfect: 4 core + 3+ profitable (squeeze optional)
        meets_most_criteria = meets_most_core  # Strong: 3+ core + 3+ profitable (squeeze optional)
        meets_some_criteria = meets_some_core  # Decent: 2+ core + 3+ profitable (minimum viable)

        # Build detailed response with profitability data
        result = {
            'meets_criteria': meets_all_criteria,
            'meets_most_criteria': meets_most_criteria,
            'meets_some_criteria': meets_some_criteria,
            'core_count': core_count,
            'profitability_count': profitability_count,
            'passes_profitability': passes_profitability,
            'criteria_count': total_criteria_count,  # For backward compatibility
            'has_squeeze_bonus': has_squeeze_bonus,
            'bb_inside_kc': bb_inside_kc,
            'ema8_rising': core_criteria['ema8_rising'],
            'atr_flip': core_criteria['atr_flip'],
            'histogram_pattern': core_criteria['histogram_pattern'],
            'momentum_rising': core_criteria['momentum_rising'],
            'momentum_value': latest['momentum'],
            'ema8_value': latest['ema8'],
            'atr_value': latest['atr'],
            'bb_upper': latest['bb_upper'],
            'bb_lower': latest['bb_lower'],
            'kc_upper': latest['kc_upper'],
            'kc_lower': latest['kc_lower'],
            'close': latest['close'],
            'volume': latest['volume'],
            'core_criteria': core_criteria,
            'profitability_filters': profitability_filters,
            'bonus_criteria': bonus_criteria,
            'criteria_summary': all_criteria
        }

        # Add failure reason if criteria not met
        if not meets_some_criteria:
            failed_core = [name for name, passed in core_criteria.items() if not passed]
            failed_profit = [name for name, passed in profitability_filters.items() if not passed]

            if not passes_profitability:
                result['reason'] = f"Failed profitability filters: {', '.join(failed_profit)} (needs 2+ profitability filters)"
            else:
                result['reason'] = f"Failed core criteria: {', '.join(failed_core)} (needs 2+ core criteria)"

        return result
    
    def grade_opportunity(self, squeeze_data: Dict) -> str:
        """Grade based on 4 CORE CRITERIA + PROFITABILITY. Squeeze is bonus only."""
        core_count = squeeze_data['core_count']
        profitability_count = squeeze_data['profitability_count']
        passes_profitability = squeeze_data['passes_profitability']
        momentum_strength = abs(squeeze_data['momentum_value'])

        # MUST PASS PROFITABILITY FIRST!
        if not passes_profitability:
            return 'F'  # Automatic fail if not profitable

        # Base grade on 4 CORE CRITERIA + PROFITABILITY (squeeze not required)
        if core_count == 4 and profitability_count == 4:
            base_grade = 'A+'  # Perfect: All 4 core + all profitability
        elif core_count == 4 and profitability_count >= 3:
            base_grade = 'A+'  # Excellent: All 4 core + strong profitability
        elif core_count == 4:
            base_grade = 'A'   # Very good: All 4 core + basic profitability
        elif core_count == 3 and profitability_count == 4:
            base_grade = 'A'   # Strong: 3 core + perfect profitability
        elif core_count == 3 and profitability_count >= 3:
            base_grade = 'B+'  # Good: 3 core + strong profitability
        elif core_count == 3:
            base_grade = 'B'   # Fair: 3 core + basic profitability
        elif core_count == 2 and profitability_count == 4:
            base_grade = 'B'   # Minimum viable: 2 core + perfect profitability
        elif core_count == 2:
            base_grade = 'C'   # Weak: 2 core + basic profitability
        else:
            return 'F'  # Less than 2 core criteria = fail

        # Adjust grade based on momentum strength
        if momentum_strength > 5.0:
            # Exceptional momentum - upgrade
            upgrades = {'A': 'A+', 'B+': 'A', 'B': 'B+', 'C': 'B'}
            base_grade = upgrades.get(base_grade, base_grade)
        elif momentum_strength > 3.0:
            # Strong momentum - small upgrade for lower grades
            if base_grade in ['B+', 'B', 'C']:
                upgrades = {'B+': 'A', 'B': 'B+', 'C': 'B'}
                base_grade = upgrades.get(base_grade, base_grade)
        elif momentum_strength < 1.0:
            # Weak momentum - downgrade
            downgrades = {'A+': 'A', 'A': 'B+', 'B+': 'B', 'B': 'C'}
            base_grade = downgrades.get(base_grade, base_grade)

        return base_grade
    
    def scan_symbol(self, symbol: str, timeframe: str = '5min') -> Optional[Dict]:
        """Scan a single symbol for TTM squeeze using STRICT 5 criteria."""
        try:
            df = self.get_historical_data(symbol, timeframe)
            if df is None or len(df) < 50:
                return None

            squeeze_data = self.detect_ttm_squeeze(df)

            # Return ONLY if meets PROFITABILITY + CORE criteria (quality-focused approach)
            if squeeze_data['meets_some_criteria']:  # At least 2 core + 3 profitability
                grade = self.grade_opportunity(squeeze_data)

                # Calculate better risk/reward based on volatility
                atr_value = squeeze_data['atr_value']
                entry_price = squeeze_data['close']

                # Dynamic stop loss based on ATR (more realistic)
                stop_loss = entry_price - (atr_value * 1.5)  # 1.5x ATR stop

                # Dynamic target based on momentum strength
                momentum_strength = abs(squeeze_data['momentum_value'])
                if momentum_strength > 3.0:
                    target_multiplier = 3.0  # Strong momentum = bigger target
                elif momentum_strength > 2.0:
                    target_multiplier = 2.5
                else:
                    target_multiplier = 2.0

                target_price = entry_price + (atr_value * target_multiplier)
                risk_reward = (target_price - entry_price) / (entry_price - stop_loss)

                # Calculate dollar amounts for risk/reward
                risk_per_share = entry_price - stop_loss
                reward_per_share = target_price - entry_price

                return {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'grade': grade,
                    'core_count': squeeze_data['core_count'],
                    'profitability_count': squeeze_data['profitability_count'],
                    'passes_profitability': squeeze_data['passes_profitability'],
                    'criteria_count': squeeze_data['criteria_count'],  # Total including all
                    'has_squeeze_bonus': squeeze_data['has_squeeze_bonus'],
                    'meets_all_criteria': squeeze_data['meets_criteria'],
                    'meets_most_criteria': squeeze_data['meets_most_criteria'],
                    'bb_inside_kc': squeeze_data['bb_inside_kc'],
                    'ema8_rising': squeeze_data['ema8_rising'],
                    'atr_flip': squeeze_data['atr_flip'],
                    'histogram_pattern': squeeze_data['histogram_pattern'],
                    'momentum_rising': squeeze_data['momentum_rising'],
                    'momentum_value': round(squeeze_data['momentum_value'], 2),
                    'ema8_value': round(squeeze_data['ema8_value'], 2),
                    'atr_value': round(squeeze_data['atr_value'], 2),
                    'price': round(squeeze_data['close'], 2),
                    'volume': squeeze_data['volume'],
                    'confidence': self.calculate_confidence(squeeze_data),
                    'entry_price': round(entry_price, 2),
                    'stop_loss': round(stop_loss, 2),
                    'target_price': round(target_price, 2),
                    'risk_reward': round(risk_reward, 1),
                    'risk_per_share': round(risk_per_share, 2),
                    'reward_per_share': round(reward_per_share, 2),
                    'core_criteria': squeeze_data['core_criteria'],
                    'profitability_filters': squeeze_data['profitability_filters'],
                    'bonus_criteria': squeeze_data['bonus_criteria'],
                    'criteria_details': squeeze_data['criteria_summary']
                }
            else:
                # Log why it failed for debugging
                if not squeeze_data['passes_profitability']:
                    info(f"❌ {symbol} failed profitability: {squeeze_data.get('reason', 'Unknown')}")
                elif squeeze_data['core_count'] <= 1:
                    info(f"❌ {symbol} failed core criteria: {squeeze_data.get('reason', 'Unknown')}")
                return None

        except Exception as e:
            warning(f"Error scanning {symbol}: {e}")
            return None
    
    def calculate_confidence(self, squeeze_data: Dict) -> float:
        """Calculate confidence based on 4 CORE CRITERIA + PROFITABILITY. Squeeze is bonus."""
        core_count = squeeze_data['core_count']
        profitability_count = squeeze_data['profitability_count']
        passes_profitability = squeeze_data['passes_profitability']
        has_squeeze_bonus = squeeze_data['has_squeeze_bonus']
        momentum_strength = abs(squeeze_data['momentum_value'])

        # MUST PASS PROFITABILITY FIRST!
        if not passes_profitability:
            return 0.1  # Very low confidence if not profitable

        # Base confidence on 4 CORE CRITERIA + PROFITABILITY (enhanced thresholds)
        if core_count == 4 and profitability_count == 4:
            confidence = 0.92  # Grade A+ baseline (90%+ target)
        elif core_count == 4 and profitability_count >= 3:
            confidence = 0.88  # Grade A+ with strong profitability
        elif core_count == 4:
            confidence = 0.82  # Grade A baseline (80%+ target)
        elif core_count == 3 and profitability_count == 4:
            confidence = 0.78  # Strong setup approaching Grade A
        elif core_count == 3 and profitability_count >= 3:
            confidence = 0.72  # Grade B+ baseline
        elif core_count == 3:
            confidence = 0.67  # Grade B baseline (65%+ target)
        elif core_count == 2 and profitability_count == 4:
            confidence = 0.63  # Minimum viable with perfect profitability
        elif core_count == 2:
            confidence = 0.55  # Minimum viable setup
        else:
            confidence = 0.3   # Below minimum

        # Squeeze bonus adds confidence (but doesn't determine grade eligibility)
        if has_squeeze_bonus:
            confidence += 0.08  # 8% bonus for squeeze (icing on the cake)

        # Adjust based on momentum strength (critical for profits)
        if momentum_strength > 5.0:
            confidence += 0.15  # Exceptional momentum boost
        elif momentum_strength > 3.0:
            confidence += 0.12  # Very strong momentum
        elif momentum_strength > 2.0:
            confidence += 0.08  # Strong momentum
        elif momentum_strength > 1.5:
            confidence += 0.05  # Good momentum
        elif momentum_strength < 1.0:
            confidence -= 0.12  # Weak momentum penalty

        return min(max(confidence, 0.1), 1.0)  # Keep between 0.1 and 1.0
    
    def scan_all_symbols(self, timeframes: List[str] = ['5min', '15min', '1hour']) -> List[Dict]:
        """Scan all symbols across multiple timeframes."""
        info(f"🔍 Scanning {len(LARGE_CAP_STOCKS)} symbols across {len(timeframes)} timeframes")
        
        opportunities = []
        
        for symbol in LARGE_CAP_STOCKS:
            for timeframe in timeframes:
                opportunity = self.scan_symbol(symbol, timeframe)
                if opportunity:
                    opportunities.append(opportunity)
        
        # Sort by grade and confidence
        grade_order = {'A+': 0, 'A': 1, 'B': 2, 'C': 3, 'D': 4, 'F': 5}
        opportunities.sort(key=lambda x: (grade_order.get(x['grade'], 5), -x['confidence']))
        
        return opportunities


def run_proper_ttm_scan() -> str:
    """Run the STRICT TTM squeeze scan with 5 criteria."""
    try:
        scanner = ProperTTMSqueezeScanner()
        opportunities = scanner.scan_all_symbols()

        if opportunities:
            result = "💎 ENHANCED TTM OPPORTUNITIES (HIGH-CONVICTION SIGNALS):\n\n"
            result += "🔥 4 CORE CRITERIA: 8EMA Rising | ATR Flip | Histogram Pattern | Momentum Rising\n"
            result += "💰 PROFITABILITY FILTERS: Enhanced Volume (150%+) | Strong Momentum | Volatility\n"
            result += "🎁 SQUEEZE BONUS: BB inside KC - Adds confidence but NOT required for Grade A!\n\n"

            # Group by enhanced grade thresholds
            a_plus_setups = [opp for opp in opportunities if opp['grade'] == 'A+' and opp['confidence'] >= 0.90]
            a_setups = [opp for opp in opportunities if opp['grade'] in ['A', 'B+'] and opp['confidence'] >= 0.80]
            b_setups = [opp for opp in opportunities if opp['grade'] in ['B', 'C'] and opp['confidence'] >= 0.65]

            if a_plus_setups:
                result += "🏆 PREMIUM SETUPS (Grade A+, 90%+ Confidence):\n"
                for opp in a_plus_setups:
                    squeeze_bonus = "🎁 +Squeeze" if opp['has_squeeze_bonus'] else "⭐ No Squeeze Needed"
                    core_score = f"🔥 {opp['core_count']}/4 Core"
                    profit_score = f"💰 {opp['profitability_count']}/4 Profit"
                    result += f"🔥 {opp['symbol']} ({opp['timeframe']}) - Grade {opp['grade']} ({opp['confidence']*100:.0f}%) {squeeze_bonus}\n"
                    result += f"   {core_score} | {profit_score} | Momentum: {opp['momentum_value']:.2f}\n"
                    result += f"   🎯 Entry: ${opp['entry_price']:.2f} | Stop: ${opp['stop_loss']:.2f} | Target: ${opp['target_price']:.2f}\n"
                    result += f"   💰 Risk: ${opp['risk_per_share']:.2f} | Reward: ${opp['reward_per_share']:.2f} | R/R: {opp['risk_reward']:.1f}:1\n\n"

            if a_setups:
                result += "⭐ QUALITY SETUPS (Grade A/B+, 80%+ Confidence):\n"
                for opp in a_setups[:10]:  # Limit to top 10
                    squeeze_bonus = "🎁 +Squeeze" if opp['has_squeeze_bonus'] else "⭐ Strong Without Squeeze"
                    core_score = f"🔥 {opp['core_count']}/4 Core"
                    profit_score = f"💰 {opp['profitability_count']}/4 Profit"
                    result += f"📈 {opp['symbol']} ({opp['timeframe']}) - Grade {opp['grade']} ({opp['confidence']*100:.0f}%) {squeeze_bonus}\n"
                    result += f"   {core_score} | {profit_score} | Momentum: {opp['momentum_value']:.2f}\n"
                    result += f"   💰 Risk: ${opp['risk_per_share']:.2f} | Reward: ${opp['reward_per_share']:.2f} | R/R: {opp['risk_reward']:.1f}:1\n"

                    # Show what's missing for transparency
                    missing_core = [name for name, passed in opp['core_criteria'].items() if not passed]
                    missing_profit = [name for name, passed in opp['profitability_filters'].items() if not passed]
                    if missing_core:
                        result += f"   ⚠️ Missing Core: {', '.join(missing_core)}\n"
                    if missing_profit:
                        result += f"   ⚠️ Missing Profit: {', '.join(missing_profit)}\n"
                    result += "\n"

            if b_setups:
                result += "📊 VIABLE SETUPS (Grade B/C, 65%+ Confidence) - Use Caution:\n"
                for opp in b_setups[:5]:  # Limit to top 5
                    squeeze_bonus = "🎁 +Squeeze" if opp['has_squeeze_bonus'] else ""
                    core_score = f"🔥 {opp['core_count']}/4 Core"
                    profit_score = f"💰 {opp['profitability_count']}/4 Profit"
                    result += f"⚠️ {opp['symbol']} ({opp['timeframe']}) - Grade {opp['grade']} ({opp['confidence']*100:.0f}%) {squeeze_bonus}\n"
                    result += f"   {core_score} | {profit_score}\n"
                    result += f"   💰 Risk: ${opp['risk_per_share']:.2f} | Reward: ${opp['reward_per_share']:.2f} | R/R: {opp['risk_reward']:.1f}:1\n\n"

            result += f"📊 ENHANCED QUALITY SUMMARY: {len(opportunities)} high-conviction opportunities found\n"
            result += f"   🏆 {len(a_plus_setups)} premium (A+, 90%+) | ⭐ {len(a_setups)} quality (A/B+, 80%+) | 📊 {len(b_setups)} viable (B/C, 65%+)\n"
            result += f"   🎁 {len([o for o in opportunities if o['has_squeeze_bonus']])} with squeeze bonus (optional)\n"
            result += f"   🔥 Avg Core Score: {sum(o['core_count'] for o in opportunities) / len(opportunities):.1f}/4\n"
            result += f"   💰 Avg Profitability Score: {sum(o['profitability_count'] for o in opportunities) / len(opportunities):.1f}/4\n"
            return result
        else:
            return """📊 No HIGH-CONVICTION opportunities found meeting enhanced criteria.

💰 ENHANCED PROFITABILITY FILTERS (Need 3+ out of 4 to qualify):
1. 💰 Price Range - Meaningful trading range for profit potential
2. 📊 Volume Above Average (150%+ of 20-day avg) - Strong institutional interest
3. 🚀 Strong Momentum (>1.5 threshold) - Decisive momentum strength
4. 📈 Enhanced Volatility (3%+ ATR) - Sufficient movement for profits

🔥 4 CORE TECHNICAL CRITERIA (Need 4/4 for Grade A+, 3/4 for Grade A):
1. ✅ 8EMA Rising (4+ consecutive periods, price above EMA, 0.5%+ sustained rise)
2. ✅ ATR Flip (15%+ decisive volatility expansion, not marginal)
3. ✅ Histogram Pattern (Momentum IMPROVEMENT - "less negative" = building pressure)
4. ✅ Momentum Rising (Sustained upward trend with acceleration over multiple periods)

🎁 SQUEEZE BONUS (Adds confidence but NOT required for Grade A):
5. 🎁 BB inside KC (Decisive squeeze condition with meaningful margin)

💡 ENHANCED QUALITY FOCUS:
- Grade A+ (90%+ confidence): 4/4 core + 3+ profitability
- Grade A (80%+ confidence): 4/4 core + basic profitability OR 3/4 core + perfect profitability
- Grade B (65%+ confidence): 3/4 core + strong profitability

Current market may lack the strong technical confluence required for high-conviction setups."""

    except Exception as e:
        return f"❌ Error running STRICT TTM scan: {str(e)}\n\n💡 Make sure FMP_API_KEY is set in config/config.env"


if __name__ == "__main__":
    print("🚀 Testing Proper TTM Squeeze Scanner")
    print("=" * 50)
    result = run_proper_ttm_scan()
    print(result)
