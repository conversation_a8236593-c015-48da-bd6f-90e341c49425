"""
Enhanced Options Scanner with Volatility Surface Analysis
Integrates with TotalRecall TTM System for Advanced Options Trading

This scanner combines:
- TTM Squeeze momentum signals with options opportunities
- Volatility surface analysis and mispricing detection
- Advanced Greeks calculation and risk management
- <PERSON> personality integration
- Professional-grade options analysis
"""

import asyncio
import aiohttp
import numpy as np
import pandas as pd
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Define logging functions first
def info(msg): print(f"INFO: {msg}")
def warning(msg): print(f"WARNING: {msg}")
def error(msg): print(f"ERROR: {msg}")

# Try to import yfinance, fall back to requests if not available
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    warning("yfinance not available, using fallback data sources")

# Try to import scipy for Black-Scholes, fall back to approximation
try:
    from scipy.stats import norm
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    # Simple normal distribution approximation
    class norm:
        @staticmethod
        def cdf(x):
            # Approximation of cumulative distribution function
            return 0.5 * (1 + np.tanh(x * np.sqrt(2 / np.pi)))

        @staticmethod
        def pdf(x):
            # Approximation of probability density function
            return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)

# Import existing TotalRecall components
try:
    from scanners.proper_ttm_squeeze_scanner import ProperTTMSqueezeScanner
    TTM_SCANNER_AVAILABLE = True
except ImportError:
    TTM_SCANNER_AVAILABLE = False
    ProperTTMSqueezeScanner = None

try:
    from logger_util import info as log_info, warning as log_warning, error as log_error
    info, warning, error = log_info, log_warning, log_error
except ImportError:
    # Use the functions already defined above
    pass

try:
    from config import get_api_key
except ImportError:
    def get_api_key(key_name):
        import os
        return os.getenv(key_name)

@dataclass
class OptionsOpportunity:
    """Enhanced options opportunity with TTM integration."""
    symbol: str
    option_type: str  # 'call' or 'put'
    strike: float
    expiration: str
    current_price: float
    bid: float
    ask: float
    volume: int
    open_interest: int
    implied_volatility: float
    theoretical_value: float
    mispricing_pct: float
    delta: float
    gamma: float
    theta: float
    vega: float
    underlying_price: float
    ttm_grade: str
    ttm_confidence: float
    strategy_type: str
    entry_price: float
    target_price: float
    stop_loss: float
    max_profit: float
    max_loss: float
    risk_reward_ratio: float
    probability_profit: float
    overall_score: float
    reasoning: str
    tony_commentary: str

class VolatilitySurfaceAnalyzer:
    """Advanced volatility surface analysis for options mispricing detection."""
    
    def __init__(self):
        self.risk_free_rate = 0.05  # Current risk-free rate
        
    def black_scholes_price(self, S: float, K: float, T: float, r: float, 
                           sigma: float, option_type: str = 'call') -> float:
        """Calculate Black-Scholes theoretical option price."""
        try:
            if T <= 0:
                return max(0, S - K) if option_type == 'call' else max(0, K - S)
            
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            if option_type == 'call':
                price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
            else:
                price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
                
            return max(0, price)
        except:
            return 0
    
    def calculate_greeks(self, S: float, K: float, T: float, r: float, 
                        sigma: float, option_type: str = 'call') -> Dict[str, float]:
        """Calculate option Greeks."""
        try:
            if T <= 0:
                return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0}
            
            d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
            d2 = d1 - sigma * np.sqrt(T)
            
            # Delta
            if option_type == 'call':
                delta = norm.cdf(d1)
            else:
                delta = norm.cdf(d1) - 1
            
            # Gamma
            gamma = norm.pdf(d1) / (S * sigma * np.sqrt(T))
            
            # Theta
            theta_part1 = -(S * norm.pdf(d1) * sigma) / (2 * np.sqrt(T))
            if option_type == 'call':
                theta_part2 = -r * K * np.exp(-r * T) * norm.cdf(d2)
                theta = (theta_part1 + theta_part2) / 365
            else:
                theta_part2 = r * K * np.exp(-r * T) * norm.cdf(-d2)
                theta = (theta_part1 + theta_part2) / 365
            
            # Vega
            vega = S * norm.pdf(d1) * np.sqrt(T) / 100
            
            return {
                'delta': round(delta, 4),
                'gamma': round(gamma, 4),
                'theta': round(theta, 4),
                'vega': round(vega, 4)
            }
        except:
            return {'delta': 0, 'gamma': 0, 'theta': 0, 'vega': 0}
    
    def analyze_volatility_surface(self, options_chain: List[Dict]) -> Dict[str, float]:
        """Analyze the volatility surface for patterns and anomalies."""
        if not options_chain:
            return {'skew': 0, 'term_structure': 0, 'surface_quality': 0}
        
        try:
            # Calculate volatility skew (put vs call IV)
            calls = [opt for opt in options_chain if opt.get('type') == 'call']
            puts = [opt for opt in options_chain if opt.get('type') == 'put']
            
            if calls and puts:
                call_iv_avg = np.mean([opt.get('impliedVolatility', 0) for opt in calls])
                put_iv_avg = np.mean([opt.get('impliedVolatility', 0) for opt in puts])
                skew = put_iv_avg - call_iv_avg
            else:
                skew = 0
            
            # Analyze term structure (near vs far dated options)
            near_term = [opt for opt in options_chain if opt.get('daysToExpiration', 0) <= 30]
            far_term = [opt for opt in options_chain if opt.get('daysToExpiration', 0) > 30]
            
            if near_term and far_term:
                near_iv = np.mean([opt.get('impliedVolatility', 0) for opt in near_term])
                far_iv = np.mean([opt.get('impliedVolatility', 0) for opt in far_term])
                term_structure = near_iv - far_iv
            else:
                term_structure = 0
            
            # Surface quality score based on data completeness
            surface_quality = min(100, len(options_chain) * 2)
            
            return {
                'skew': round(skew, 4),
                'term_structure': round(term_structure, 4),
                'surface_quality': round(surface_quality, 1)
            }
        except:
            return {'skew': 0, 'term_structure': 0, 'surface_quality': 0}

class EnhancedOptionsScanner:
    """Enhanced options scanner with TTM integration and volatility analysis."""
    
    def __init__(self):
        self.ttm_scanner = ProperTTMSqueezeScanner() if TTM_SCANNER_AVAILABLE else None
        self.vol_analyzer = VolatilitySurfaceAnalyzer()
        self.api_key = get_api_key('FMP_API_KEY')
        
        # Focus on liquid, tradeable options
        self.target_symbols = [
            'SPY', 'QQQ', 'IWM', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 
            'TSLA', 'META', 'NFLX', 'AMD', 'CRM', 'ADBE', 'ORCL'
        ]
        
    def get_options_chain(self, symbol: str) -> List[Dict]:
        """Get options chain using Alpaca API as primary source."""
        try:
            # Try Alpaca first for real options data
            alpaca_options = self._get_alpaca_options_chain(symbol)
            if alpaca_options:
                info(f"✅ Got {len(alpaca_options)} real options from Alpaca for {symbol}")
                return alpaca_options

            # Fallback to yfinance if available
            if YFINANCE_AVAILABLE:
                warning(f"Alpaca failed for {symbol}, trying yfinance...")
                ticker = yf.Ticker(symbol)
                options_dates = ticker.options

                if not options_dates:
                    return self._get_mock_options_chain(symbol)

                all_options = []

                # Get options for next 2 expiration dates (30-60 days focus)
                for exp_date in options_dates[:2]:
                    try:
                        calls = ticker.option_chain(exp_date).calls
                        puts = ticker.option_chain(exp_date).puts

                        # Process calls
                        for _, call in calls.iterrows():
                            if call['volume'] > 10 and call['openInterest'] > 50:
                                all_options.append({
                                    'type': 'call',
                                    'strike': call['strike'],
                                    'expiration': exp_date,
                                    'bid': call['bid'],
                                    'ask': call['ask'],
                                    'lastPrice': call['lastPrice'],
                                    'volume': call['volume'],
                                    'openInterest': call['openInterest'],
                                    'impliedVolatility': call['impliedVolatility'],
                                    'daysToExpiration': (datetime.strptime(exp_date, '%Y-%m-%d') - datetime.now()).days
                                })

                        # Process puts
                        for _, put in puts.iterrows():
                            if put['volume'] > 10 and put['openInterest'] > 50:
                                all_options.append({
                                    'type': 'put',
                                    'strike': put['strike'],
                                    'expiration': exp_date,
                                    'bid': put['bid'],
                                    'ask': put['ask'],
                                    'lastPrice': put['lastPrice'],
                                    'volume': put['volume'],
                                    'openInterest': put['openInterest'],
                                    'impliedVolatility': put['impliedVolatility'],
                                    'daysToExpiration': (datetime.strptime(exp_date, '%Y-%m-%d') - datetime.now()).days
                                })

                    except Exception as e:
                        warning(f"Failed to get options for {symbol} {exp_date}: {e}")
                        continue

                return all_options if all_options else self._get_mock_options_chain(symbol)
            else:
                # Final fallback to mock data
                warning(f"No real data sources available for {symbol}, using mock data")
                return self._get_mock_options_chain(symbol)

        except Exception as e:
            warning(f"Failed to get options chain for {symbol}: {e}")
            return self._get_mock_options_chain(symbol)

    def _get_mock_options_chain(self, symbol: str) -> List[Dict]:
        """Generate mock options chain for demonstration purposes."""
        # Get approximate current price (mock)
        base_price = {'SPY': 450, 'QQQ': 380, 'AAPL': 180, 'MSFT': 380, 'GOOGL': 140}.get(symbol, 100)

        mock_options = []

        # Generate mock options around current price
        for days_out in [14, 30]:  # Two expiration dates
            exp_date = (datetime.now() + timedelta(days=days_out)).strftime('%Y-%m-%d')

            for strike_offset in [-20, -10, 0, 10, 20]:  # 5 strikes around current price
                strike = base_price + strike_offset

                # Mock call option
                mock_options.append({
                    'type': 'call',
                    'strike': strike,
                    'expiration': exp_date,
                    'bid': max(0.5, (base_price - strike + 5) * 0.1),
                    'ask': max(0.7, (base_price - strike + 7) * 0.1),
                    'lastPrice': max(0.6, (base_price - strike + 6) * 0.1),
                    'volume': np.random.randint(50, 500),
                    'openInterest': np.random.randint(100, 2000),
                    'impliedVolatility': 0.2 + np.random.random() * 0.3,
                    'daysToExpiration': days_out
                })

                # Mock put option
                mock_options.append({
                    'type': 'put',
                    'strike': strike,
                    'expiration': exp_date,
                    'bid': max(0.5, (strike - base_price + 5) * 0.1),
                    'ask': max(0.7, (strike - base_price + 7) * 0.1),
                    'lastPrice': max(0.6, (strike - base_price + 6) * 0.1),
                    'volume': np.random.randint(50, 500),
                    'openInterest': np.random.randint(100, 2000),
                    'impliedVolatility': 0.25 + np.random.random() * 0.3,
                    'daysToExpiration': days_out
                })

        return mock_options

    def _get_alpaca_options_chain(self, symbol: str) -> List[Dict]:
        """Get options chain from Alpaca API."""
        try:
            # Get Alpaca API credentials
            api_key = get_api_key('ALPACA_API_KEY')
            api_secret = get_api_key('ALPACA_SECRET_KEY')

            if not api_key or not api_secret:
                warning("Alpaca API credentials not found")
                return []

            # Alpaca options endpoints
            base_url = "https://data.alpaca.markets"
            headers = {
                "APCA-API-KEY-ID": api_key,
                "APCA-API-SECRET-KEY": api_secret,
                "accept": "application/json"
            }

            all_options = []

            # Get options snapshots for the symbol
            # Alpaca options API endpoint for snapshots
            snapshot_url = f"{base_url}/v1beta1/options/snapshots"

            # Get options contracts first
            contracts_url = f"{base_url}/v1beta1/options/contracts"
            params = {
                "underlying_symbols": symbol,
                "status": "active",
                "expiration_date_gte": datetime.now().strftime('%Y-%m-%d'),
                "expiration_date_lte": (datetime.now() + timedelta(days=60)).strftime('%Y-%m-%d'),
                "limit": 1000
            }

            response = requests.get(contracts_url, headers=headers, params=params)

            if response.status_code == 200:
                contracts_data = response.json()
                option_contracts = contracts_data.get('option_contracts', [])

                info(f"Found {len(option_contracts)} option contracts for {symbol}")

                # Get snapshots for these contracts in batches
                contract_symbols = [contract['symbol'] for contract in option_contracts]

                # Process in batches of 100 (Alpaca limit)
                for i in range(0, len(contract_symbols), 100):
                    batch_symbols = contract_symbols[i:i+100]

                    snapshot_params = {
                        "symbols": ",".join(batch_symbols)
                    }

                    snapshot_response = requests.get(snapshot_url, headers=headers, params=snapshot_params)

                    if snapshot_response.status_code == 200:
                        snapshot_data = snapshot_response.json()
                        snapshots = snapshot_data.get('snapshots', {})

                        # Process each option snapshot
                        for contract_symbol, snapshot in snapshots.items():
                            # Find the corresponding contract info
                            contract_info = next((c for c in option_contracts if c['symbol'] == contract_symbol), None)
                            if not contract_info:
                                continue

                            # Extract option data
                            latest_quote = snapshot.get('latestQuote', {})
                            latest_trade = snapshot.get('latestTrade', {})
                            greeks = snapshot.get('greeks', {})

                            # Calculate days to expiration
                            exp_date = contract_info.get('expiration_date', '')
                            if exp_date:
                                exp_datetime = datetime.strptime(exp_date, '%Y-%m-%d')
                                days_to_exp = (exp_datetime - datetime.now()).days
                            else:
                                days_to_exp = 0

                            # Filter for liquid options and reasonable expiration
                            volume = latest_trade.get('size', 0) if latest_trade else 0
                            open_interest = snapshot.get('openInterest', 0)

                            if volume >= 10 and open_interest >= 50 and 7 <= days_to_exp <= 60:
                                option_data = {
                                    'type': contract_info.get('type', '').lower(),  # 'call' or 'put'
                                    'strike': float(contract_info.get('strike_price', 0)),
                                    'expiration': exp_date,
                                    'bid': float(latest_quote.get('bidprice', 0)) if latest_quote else 0,
                                    'ask': float(latest_quote.get('askprice', 0)) if latest_quote else 0,
                                    'lastPrice': float(latest_trade.get('price', 0)) if latest_trade else 0,
                                    'volume': volume,
                                    'openInterest': open_interest,
                                    'impliedVolatility': float(greeks.get('impliedVolatility', 0.3)),
                                    'daysToExpiration': days_to_exp,
                                    'delta': float(greeks.get('delta', 0)),
                                    'gamma': float(greeks.get('gamma', 0)),
                                    'theta': float(greeks.get('theta', 0)),
                                    'vega': float(greeks.get('vega', 0))
                                }

                                all_options.append(option_data)

                    else:
                        warning(f"Failed to get snapshots batch {i//100 + 1}: {snapshot_response.status_code}")

                info(f"Processed {len(all_options)} liquid options for {symbol}")
                return all_options

            else:
                warning(f"Failed to get option contracts for {symbol}: {response.status_code}")
                return []

        except Exception as e:
            error(f"Alpaca options API error for {symbol}: {e}")
            return []

    def _get_current_price(self, symbol: str) -> Optional[float]:
        """Get current stock price using Alpaca API with fallback methods."""
        try:
            # Try Alpaca first for real-time data
            alpaca_price = self._get_alpaca_current_price(symbol)
            if alpaca_price:
                return alpaca_price

            # Fallback to yfinance if available
            if YFINANCE_AVAILABLE:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="1d")
                if not hist.empty:
                    return hist['Close'].iloc[-1]

            # Final fallback to mock prices for demonstration
            mock_prices = {
                'SPY': 450.0, 'QQQ': 380.0, 'IWM': 200.0,
                'AAPL': 180.0, 'MSFT': 380.0, 'GOOGL': 140.0,
                'AMZN': 150.0, 'NVDA': 500.0, 'TSLA': 250.0,
                'META': 350.0, 'NFLX': 450.0, 'AMD': 120.0,
                'CRM': 250.0, 'ADBE': 550.0, 'ORCL': 110.0
            }
            return mock_prices.get(symbol, 100.0)

        except Exception as e:
            warning(f"Failed to get price for {symbol}: {e}")
            return None

    def _get_alpaca_current_price(self, symbol: str) -> Optional[float]:
        """Get current stock price from Alpaca API."""
        try:
            api_key = get_api_key('ALPACA_API_KEY')
            api_secret = get_api_key('ALPACA_SECRET_KEY')

            if not api_key or not api_secret:
                return None

            headers = {
                "APCA-API-KEY-ID": api_key,
                "APCA-API-SECRET-KEY": api_secret,
                "accept": "application/json"
            }

            # Get latest quote from Alpaca
            url = f"https://data.alpaca.markets/v2/stocks/{symbol}/quotes/latest"
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                quote = data.get('quote', {})
                bid = quote.get('bp', 0)
                ask = quote.get('ap', 0)

                if bid > 0 and ask > 0:
                    return (bid + ask) / 2  # Mid price
                elif ask > 0:
                    return ask
                elif bid > 0:
                    return bid

            return None

        except Exception as e:
            warning(f"Alpaca price API error for {symbol}: {e}")
            return None

    def analyze_option_opportunity(self, option: Dict, underlying_price: float,
                                 ttm_data: Dict = None) -> Optional[OptionsOpportunity]:
        """Analyze individual option for trading opportunity."""
        try:
            # Basic option data
            strike = option['strike']
            option_type = option['type']
            expiration = option['expiration']
            bid = option['bid']
            ask = option['ask']
            mid_price = (bid + ask) / 2
            volume = option['volume']
            open_interest = option['openInterest']
            implied_vol = option['impliedVolatility']

            # Calculate days to expiration
            exp_date = datetime.strptime(expiration, '%Y-%m-%d')
            days_to_exp = (exp_date - datetime.now()).days
            time_to_exp = days_to_exp / 365.0

            # Skip if too close to expiration or too far out
            if days_to_exp < 7 or days_to_exp > 60:
                return None

            # Calculate theoretical value using Black-Scholes
            theoretical_value = self.vol_analyzer.black_scholes_price(
                underlying_price, strike, time_to_exp, 0.05, implied_vol, option_type
            )

            # Calculate mispricing percentage
            if theoretical_value > 0:
                mispricing_pct = ((theoretical_value - mid_price) / theoretical_value) * 100
            else:
                mispricing_pct = 0

            # Skip if mispricing is not significant enough
            if abs(mispricing_pct) < 10:
                return None

            # Calculate Greeks
            greeks = self.vol_analyzer.calculate_greeks(
                underlying_price, strike, time_to_exp, 0.05, implied_vol, option_type
            )

            # Determine strategy type and risk/reward
            strategy_type, entry_price, target_price, stop_loss = self._determine_strategy(
                option_type, mid_price, mispricing_pct, underlying_price, strike
            )

            # Calculate profit/loss potential
            max_profit = target_price - entry_price if target_price > entry_price else 0
            max_loss = entry_price - stop_loss if entry_price > stop_loss else entry_price
            risk_reward_ratio = max_profit / max_loss if max_loss > 0 else 0

            # Calculate probability of profit (simplified)
            moneyness = underlying_price / strike if option_type == 'call' else strike / underlying_price
            prob_profit = self._calculate_probability_profit(
                option_type, moneyness, time_to_exp, implied_vol
            )

            # Get TTM data for integration
            ttm_grade = ttm_data.get('grade', 'N/A') if ttm_data else 'N/A'
            ttm_confidence = ttm_data.get('confidence', 0) if ttm_data else 0

            # Calculate overall score
            overall_score = self._calculate_overall_score(
                mispricing_pct, volume, open_interest, risk_reward_ratio,
                prob_profit, ttm_confidence, days_to_exp
            )

            # Generate reasoning and Tony commentary
            reasoning = self._generate_reasoning(
                option_type, mispricing_pct, volume, open_interest,
                risk_reward_ratio, ttm_grade
            )

            tony_commentary = self._generate_tony_commentary(
                option_type, strike, underlying_price, mispricing_pct, ttm_grade
            )

            return OptionsOpportunity(
                symbol=option.get('symbol', 'UNKNOWN'),
                option_type=option_type,
                strike=strike,
                expiration=expiration,
                current_price=mid_price,
                bid=bid,
                ask=ask,
                volume=volume,
                open_interest=open_interest,
                implied_volatility=implied_vol,
                theoretical_value=theoretical_value,
                mispricing_pct=mispricing_pct,
                delta=greeks['delta'],
                gamma=greeks['gamma'],
                theta=greeks['theta'],
                vega=greeks['vega'],
                underlying_price=underlying_price,
                ttm_grade=ttm_grade,
                ttm_confidence=ttm_confidence,
                strategy_type=strategy_type,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                max_profit=max_profit,
                max_loss=max_loss,
                risk_reward_ratio=risk_reward_ratio,
                probability_profit=prob_profit,
                overall_score=overall_score,
                reasoning=reasoning,
                tony_commentary=tony_commentary
            )

        except Exception as e:
            warning(f"Failed to analyze option opportunity: {e}")
            return None

    def _determine_strategy(self, option_type: str, mid_price: float, mispricing_pct: float,
                          underlying_price: float, strike: float) -> Tuple[str, float, float, float]:
        """Determine optimal strategy based on mispricing and market conditions."""

        if mispricing_pct > 10:  # Option is undervalued
            strategy_type = f"Long {option_type.title()}"
            entry_price = mid_price
            target_price = mid_price * 1.5  # 50% profit target
            stop_loss = mid_price * 0.5     # 50% stop loss

        elif mispricing_pct < -10:  # Option is overvalued
            strategy_type = f"Short {option_type.title()}"
            entry_price = mid_price
            target_price = mid_price * 0.7  # 30% profit target
            stop_loss = mid_price * 1.3     # 30% stop loss

        else:
            # Neutral strategy
            strategy_type = f"Neutral {option_type.title()}"
            entry_price = mid_price
            target_price = mid_price * 1.2
            stop_loss = mid_price * 0.8

        return strategy_type, entry_price, target_price, stop_loss

    def _calculate_probability_profit(self, option_type: str, moneyness: float,
                                    time_to_exp: float, implied_vol: float) -> float:
        """Calculate simplified probability of profit."""
        try:
            # Base probability on moneyness and time
            if option_type == 'call':
                base_prob = 50 + (moneyness - 1) * 100
            else:
                base_prob = 50 + (1 - moneyness) * 100

            # Adjust for time decay
            time_factor = max(0.5, time_to_exp * 12)  # Favor longer-dated options

            # Adjust for volatility
            vol_factor = min(1.2, implied_vol * 2)

            prob_profit = base_prob * time_factor * vol_factor
            return max(10, min(90, prob_profit))

        except:
            return 50

    def _calculate_overall_score(self, mispricing_pct: float, volume: int,
                               open_interest: int, risk_reward_ratio: float,
                               prob_profit: float, ttm_confidence: float,
                               days_to_exp: int) -> float:
        """Calculate overall opportunity score (0-100)."""
        try:
            # Mispricing score (0-30 points)
            mispricing_score = min(30, abs(mispricing_pct))

            # Liquidity score (0-20 points)
            liquidity_score = min(20, (volume / 100) + (open_interest / 500))

            # Risk/reward score (0-20 points)
            rr_score = min(20, risk_reward_ratio * 5)

            # Probability score (0-15 points)
            prob_score = prob_profit * 0.15

            # TTM integration score (0-10 points)
            ttm_score = ttm_confidence * 0.1 if ttm_confidence > 0 else 0

            # Time decay penalty (0-5 points deduction)
            time_penalty = max(0, (30 - days_to_exp) * 0.2)

            total_score = mispricing_score + liquidity_score + rr_score + prob_score + ttm_score - time_penalty
            return max(0, min(100, total_score))

        except:
            return 50

    def _generate_reasoning(self, option_type: str, mispricing_pct: float, volume: int,
                          open_interest: int, risk_reward_ratio: float, ttm_grade: str) -> str:
        """Generate human-readable reasoning for the opportunity."""
        reasons = []

        if abs(mispricing_pct) > 15:
            reasons.append(f"Significant mispricing: {mispricing_pct:+.1f}%")
        elif abs(mispricing_pct) > 10:
            reasons.append(f"Moderate mispricing: {mispricing_pct:+.1f}%")

        if volume > 500:
            reasons.append(f"High volume: {volume:,}")
        elif volume > 100:
            reasons.append(f"Good volume: {volume:,}")

        if open_interest > 1000:
            reasons.append(f"Strong OI: {open_interest:,}")

        if risk_reward_ratio > 2:
            reasons.append(f"Excellent R/R: {risk_reward_ratio:.1f}:1")
        elif risk_reward_ratio > 1.5:
            reasons.append(f"Good R/R: {risk_reward_ratio:.1f}:1")

        if ttm_grade in ['A+', 'A']:
            reasons.append(f"TTM Grade {ttm_grade} momentum")

        return " | ".join(reasons) if reasons else "Standard opportunity"

    def _generate_tony_commentary(self, option_type: str, strike: float,
                                underlying_price: float, mispricing_pct: float,
                                ttm_grade: str) -> str:
        """Generate Tony Bellarosa's colorful options commentary."""

        # Tony's personality: volatile, profanity-laced, street-smart trader
        if mispricing_pct > 15:
            if option_type == 'call':
                return f"🔥 HOLY SHIT! This {strike} call is cheaper than my ex-wife's cooking! Market's givin' us a GIFT here - grab it before some Wall Street prick notices!"
            else:
                return f"💰 BADA-BING! This {strike} put is mispriced like a stolen Rolex! These idiots don't know what they got - we're takin' their lunch money!"

        elif mispricing_pct < -15:
            return f"🎯 Listen up, genius - this option's more overpriced than a Manhattan parking spot! Time to SHORT this piece of garbage and watch it burn!"

        elif ttm_grade in ['A+', 'A']:
            return f"⚡ TTM's screamin' Grade {ttm_grade} and this option's sittin' pretty! It's like findin' a winning lottery ticket in your old jacket - PLAY IT!"

        else:
            return f"📊 Decent setup, but don't bet the farm on it. This ain't gonna make you rich overnight, but it beats losin' money on crypto like a schmuck!"

    def scan_symbol_options(self, symbol: str) -> List[OptionsOpportunity]:
        """Scan options for a single symbol with TTM integration."""
        try:
            info(f"🔍 Scanning options for {symbol}")

            # Get current stock price
            current_price = self._get_current_price(symbol)
            if current_price is None:
                return []

            # Get TTM analysis for the underlying
            ttm_data = None
            if self.ttm_scanner:
                try:
                    ttm_result = self.ttm_scanner.scan_symbol(symbol, '5min')
                    if ttm_result:
                        ttm_data = {
                            'grade': ttm_result.get('grade', 'N/A'),
                            'confidence': ttm_result.get('confidence', 0),
                            'core_count': ttm_result.get('core_count', 0)
                        }
                except Exception as e:
                    warning(f"TTM analysis failed for {symbol}: {e}")
            else:
                warning("TTM scanner not available, using standalone options analysis")

            # Get options chain
            options_chain = self.get_options_chain(symbol)
            if not options_chain:
                return []

            # Add symbol to each option
            for option in options_chain:
                option['symbol'] = symbol

            # Analyze volatility surface
            vol_surface = self.vol_analyzer.analyze_volatility_surface(options_chain)

            # Analyze each option
            opportunities = []
            for option in options_chain:
                opportunity = self.analyze_option_opportunity(option, current_price, ttm_data)
                if opportunity and opportunity.overall_score > 60:  # Quality threshold
                    opportunities.append(opportunity)

            # Sort by overall score
            opportunities.sort(key=lambda x: x.overall_score, reverse=True)

            info(f"✅ Found {len(opportunities)} quality options opportunities for {symbol}")
            return opportunities[:5]  # Return top 5 per symbol

        except Exception as e:
            error(f"Failed to scan options for {symbol}: {e}")
            return []

    def scan_all_opportunities(self, max_symbols: int = 10) -> List[OptionsOpportunity]:
        """Scan all target symbols for options opportunities."""
        info(f"🚀 Starting enhanced options scan across {max_symbols} symbols")

        all_opportunities = []
        symbols_processed = 0

        for symbol in self.target_symbols[:max_symbols]:
            try:
                opportunities = self.scan_symbol_options(symbol)
                all_opportunities.extend(opportunities)
                symbols_processed += 1

                info(f"📊 Processed {symbol}: {len(opportunities)} opportunities")

            except Exception as e:
                warning(f"Failed to process {symbol}: {e}")
                continue

        # Sort all opportunities by score
        all_opportunities.sort(key=lambda x: x.overall_score, reverse=True)

        info(f"🎯 Scan complete: {len(all_opportunities)} total opportunities from {symbols_processed} symbols")
        return all_opportunities[:15]  # Return top 15 overall

    def format_results(self, opportunities: List[OptionsOpportunity]) -> str:
        """Format results in TotalRecall style with Tony's commentary."""
        if not opportunities:
            return """🔍 ENHANCED OPTIONS SCAN RESULTS:

❌ No high-quality options opportunities found meeting our criteria.

🎯 ENHANCED CRITERIA REQUIREMENTS:
• Mispricing threshold: >10% vs theoretical value
• Liquidity: Volume >10, Open Interest >50
• Time frame: 7-60 days to expiration
• Quality score: >60/100
• TTM integration: Momentum confluence preferred

💡 Tony says: "Market's tighter than a nun's... well, you know. Come back when there's some real action!"
"""

        result = "🔥 ENHANCED OPTIONS OPPORTUNITIES (TTM INTEGRATED):\n\n"
        result += "💎 Combining volatility surface analysis with TTM momentum signals\n"
        result += "🎯 Focus: Liquid options with significant mispricing (>10% threshold)\n"
        result += "⚡ Integration: TTM Squeeze momentum + Options volatility edge\n\n"

        # Group by score ranges
        premium_ops = [op for op in opportunities if op.overall_score >= 80]
        quality_ops = [op for op in opportunities if 70 <= op.overall_score < 80]
        viable_ops = [op for op in opportunities if 60 <= op.overall_score < 70]

        if premium_ops:
            result += "🏆 PREMIUM OPPORTUNITIES (80+ Score):\n"
            for op in premium_ops[:5]:
                result += f"🔥 {op.symbol} {op.strike}{op.option_type[0].upper()} {op.expiration} - Score: {op.overall_score:.0f}\n"
                result += f"   💰 Entry: ${op.entry_price:.2f} | Target: ${op.target_price:.2f} | Stop: ${op.stop_loss:.2f}\n"
                result += f"   📊 Risk: ${op.max_loss:.2f} | Reward: ${op.max_profit:.2f} | R/R: {op.risk_reward_ratio:.1f}:1\n"
                result += f"   🎯 Mispricing: {op.mispricing_pct:+.1f}% | TTM: {op.ttm_grade} ({op.ttm_confidence*100:.0f}%)\n"
                result += f"   🗣️ Tony: {op.tony_commentary}\n\n"

        if quality_ops:
            result += "⭐ QUALITY OPPORTUNITIES (70-79 Score):\n"
            for op in quality_ops[:5]:
                result += f"📈 {op.symbol} {op.strike}{op.option_type[0].upper()} {op.expiration} - Score: {op.overall_score:.0f}\n"
                result += f"   💰 Risk: ${op.max_loss:.2f} | Reward: ${op.max_profit:.2f} | R/R: {op.risk_reward_ratio:.1f}:1\n"
                result += f"   📊 {op.reasoning}\n\n"

        if viable_ops:
            result += "📊 VIABLE OPPORTUNITIES (60-69 Score):\n"
            for op in viable_ops[:3]:
                result += f"⚠️ {op.symbol} {op.strike}{op.option_type[0].upper()} {op.expiration} - Score: {op.overall_score:.0f}\n"
                result += f"   💰 R/R: {op.risk_reward_ratio:.1f}:1 | Mispricing: {op.mispricing_pct:+.1f}%\n\n"

        result += f"📊 SCAN SUMMARY:\n"
        result += f"   🏆 {len(premium_ops)} premium | ⭐ {len(quality_ops)} quality | 📊 {len(viable_ops)} viable\n"
        result += f"   🎯 Average Score: {sum(op.overall_score for op in opportunities) / len(opportunities):.1f}/100\n"
        result += f"   💰 Average R/R: {sum(op.risk_reward_ratio for op in opportunities) / len(opportunities):.1f}:1\n"

        return result

# Main function for integration with TotalRecall
def run_enhanced_options_scan(max_symbols: int = 10) -> str:
    """Main function to run enhanced options scan."""
    try:
        scanner = EnhancedOptionsScanner()
        opportunities = scanner.scan_all_opportunities(max_symbols)
        return scanner.format_results(opportunities)

    except Exception as e:
        error(f"Enhanced options scan failed: {e}")
        return f"❌ Enhanced options scan failed: {str(e)}\n\n💡 Check market data connections and try again."

if __name__ == "__main__":
    print("🚀 Testing Enhanced Options Scanner")
    print("=" * 50)
    result = run_enhanced_options_scan(5)
    print(result)
